import fs from 'fs';
import path from 'path';

// 文件管理工具类
export class FileManager {
  private static instance: FileManager;
  private baseDir: string;

  private constructor() {
    this.baseDir = process.cwd();
  }

  public static getInstance(): FileManager {
    if (!FileManager.instance) {
      FileManager.instance = new FileManager();
    }
    return FileManager.instance;
  }

  // 确保目录存在
  public ensureDir(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  // 获取novels目录路径
  public getNovelsDir(): string {
    return path.join(this.baseDir, '..', 'novels');
  }

  // 获取chapters目录路径
  public getChaptersDir(): string {
    return path.join(this.baseDir, '..', 'chapters');
  }

  // 获取数据目录路径
  public getDataDir(): string {
    const dataDir = path.join(this.baseDir, 'data');
    this.ensureDir(dataDir);
    return dataDir;
  }

  // 获取改写结果目录路径
  public getRewrittenDir(): string {
    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');
    this.ensureDir(rewrittenDir);
    return rewrittenDir;
  }

  // 获取特定小说的改写结果目录
  public getNovelRewrittenDir(novelTitle: string): string {
    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));
    this.ensureDir(novelDir);
    return novelDir;
  }

  // 获取特定小说的章节目录
  public getNovelChaptersDir(novelTitle: string): string {
    const chaptersDir = this.getChaptersDir();
    this.ensureDir(chaptersDir);
    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));
    this.ensureDir(novelDir);
    return novelDir;
  }

  // 清理文件名中的非法字符
  public sanitizeFilename(filename: string): string {
    return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
  }

  // 读取文件内容
  public readFile(filePath: string): string {
    try {
      return fs.readFileSync(filePath, 'utf-8');
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error);
      throw error;
    }
  }

  // 写入文件内容
  public writeFile(filePath: string, content: string): void {
    try {
      const dir = path.dirname(filePath);
      this.ensureDir(dir);
      fs.writeFileSync(filePath, content, 'utf-8');
    } catch (error) {
      console.error(`写入文件失败: ${filePath}`, error);
      throw error;
    }
  }

  // 检查文件是否存在
  public fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }

  // 获取目录中的所有文件
  public listFiles(dirPath: string, extensions?: string[]): string[] {
    try {
      if (!fs.existsSync(dirPath)) {
        return [];
      }

      const files = fs.readdirSync(dirPath);
      
      if (extensions) {
        return files.filter(file => {
          const ext = path.extname(file).toLowerCase();
          return extensions.includes(ext);
        });
      }

      return files;
    } catch (error) {
      console.error(`读取目录失败: ${dirPath}`, error);
      return [];
    }
  }

  // 获取文件信息
  public getFileStats(filePath: string): fs.Stats | null {
    try {
      return fs.statSync(filePath);
    } catch (error) {
      console.error(`获取文件信息失败: ${filePath}`, error);
      return null;
    }
  }

  // 删除文件
  public deleteFile(filePath: string): boolean {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`删除文件失败: ${filePath}`, error);
      return false;
    }
  }

  // 删除目录
  public deleteDir(dirPath: string): boolean {
    try {
      if (fs.existsSync(dirPath)) {
        fs.rmSync(dirPath, { recursive: true, force: true });
        return true;
      }
      return false;
    } catch (error) {
      console.error(`删除目录失败: ${dirPath}`, error);
      return false;
    }
  }

  // 复制文件
  public copyFile(srcPath: string, destPath: string): boolean {
    try {
      const destDir = path.dirname(destPath);
      this.ensureDir(destDir);
      fs.copyFileSync(srcPath, destPath);
      return true;
    } catch (error) {
      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);
      return false;
    }
  }

  // 移动文件
  public moveFile(srcPath: string, destPath: string): boolean {
    try {
      const destDir = path.dirname(destPath);
      this.ensureDir(destDir);
      fs.renameSync(srcPath, destPath);
      return true;
    } catch (error) {
      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);
      return false;
    }
  }

  // 获取目录大小
  public getDirSize(dirPath: string): number {
    let totalSize = 0;
    
    try {
      if (!fs.existsSync(dirPath)) {
        return 0;
      }

      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += this.getDirSize(filePath);
        } else {
          totalSize += stats.size;
        }
      }
    } catch (error) {
      console.error(`计算目录大小失败: ${dirPath}`, error);
    }

    return totalSize;
  }

  // 格式化文件大小
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 创建备份
  public createBackup(filePath: string): string | null {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const ext = path.extname(filePath);
      const baseName = path.basename(filePath, ext);
      const dir = path.dirname(filePath);
      
      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);
      
      if (this.copyFile(filePath, backupPath)) {
        return backupPath;
      }
      
      return null;
    } catch (error) {
      console.error(`创建备份失败: ${filePath}`, error);
      return null;
    }
  }

  // 清理旧备份文件
  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {
    try {
      const files = this.listFiles(dirPath);
      const backupFiles = files
        .filter(file => file.includes('_backup_'))
        .map(file => ({
          name: file,
          path: path.join(dirPath, file),
          stats: this.getFileStats(path.join(dirPath, file))
        }))
        .filter(item => item.stats !== null)
        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());

      // 删除超出数量限制的备份文件
      if (backupFiles.length > maxBackups) {
        const filesToDelete = backupFiles.slice(maxBackups);
        for (const file of filesToDelete) {
          this.deleteFile(file.path);
        }
      }
    } catch (error) {
      console.error(`清理备份文件失败: ${dirPath}`, error);
    }
  }
}

// 导出单例实例
export const fileManager = FileManager.getInstance();
