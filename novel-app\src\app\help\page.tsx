'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, BookOpen, Wand2, Target, Lightbulb, CheckCircle, XCircle } from 'lucide-react';

export default function HelpPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('basics');

  const categories = {
    basics: {
      title: '基础概念',
      icon: BookOpen,
      content: (
        <div className="space-y-6">
          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">什么是改写规则？</h3>
            <p className="text-gray-700 mb-4">
              改写规则是告诉AI如何修改小说内容的指令。通过详细的规则描述，AI可以按照你的要求对小说进行个性化改写。
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">规则的作用</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• 指导AI理解你的改写需求</li>
                <li>• 确保改写结果符合你的期望</li>
                <li>• 保持故事的连贯性和逻辑性</li>
                <li>• 个性化定制阅读体验</li>
              </ul>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">如何编写有效的规则？</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <CheckCircle className="text-green-600 mr-2" size={16} />
                  <h4 className="font-medium text-green-800">好的规则</h4>
                </div>
                <ul className="text-green-700 text-sm space-y-1">
                  <li>• 具体明确的描述</li>
                  <li>• 包含具体的改写方向</li>
                  <li>• 考虑故事的整体性</li>
                  <li>• 适度的改写幅度</li>
                </ul>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <XCircle className="text-red-600 mr-2" size={16} />
                  <h4 className="font-medium text-red-800">避免的问题</h4>
                </div>
                <ul className="text-red-700 text-sm space-y-1">
                  <li>• 过于模糊的描述</li>
                  <li>• 相互矛盾的要求</li>
                  <li>• 过度的改写要求</li>
                  <li>• 忽略故事逻辑</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      )
    },
    examples: {
      title: '规则示例',
      icon: Wand2,
      content: (
        <div className="space-y-6">
          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">感情戏增强</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
{`请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展
5. 添加更多的日常生活场景和温馨互动`}
              </pre>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">毒点清除</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
{`请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 修正主角的三观和行为逻辑
4. 用更合理的情节替代被删除的内容
5. 确保故事逻辑的完整性和连贯性`}
              </pre>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">人设优化</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
{`请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 增强角色的智商和情商表现
5. 保持角色的核心特征，但优化表现方式`}
              </pre>
            </div>
          </section>
        </div>
      )
    },
    tips: {
      title: '写作技巧',
      icon: Target,
      content: (
        <div className="space-y-6">
          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">规则编写技巧</h3>
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">1. 使用具体的动词</h4>
                <p className="text-gray-700 text-sm mb-2">用"扩写"、"删除"、"修改"等具体动词，而不是"优化"、"改善"等模糊词汇。</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div className="bg-green-50 p-2 rounded">
                    <span className="text-green-600 font-medium">好：</span> 扩写男女主对话
                  </div>
                  <div className="bg-red-50 p-2 rounded">
                    <span className="text-red-600 font-medium">差：</span> 优化感情戏
                  </div>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">2. 设定优先级</h4>
                <p className="text-gray-700 text-sm mb-2">用数字标号明确各项规则的重要程度。</p>
                <div className="bg-blue-50 p-3 rounded text-sm">
                  <div className="text-blue-800">
                    1. 首要任务：删除毒点情节<br/>
                    2. 次要任务：扩写感情戏<br/>
                    3. 可选任务：优化对话风格
                  </div>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">3. 提供具体例子</h4>
                <p className="text-gray-700 text-sm mb-2">在规则中包含具体的例子或场景描述。</p>
                <div className="bg-yellow-50 p-3 rounded text-sm">
                  <div className="text-yellow-800">
                    例如：将"他很强"改写为具体的实力展现，如"他一剑斩断了千年古树"
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">常见问题解决</h3>
            <div className="space-y-3">
              <details className="border border-gray-200 rounded-lg">
                <summary className="p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50">
                  改写结果不符合预期怎么办？
                </summary>
                <div className="p-3 border-t border-gray-200 text-sm text-gray-700">
                  <ul className="space-y-1">
                    <li>• 检查规则是否足够具体和明确</li>
                    <li>• 避免相互矛盾的要求</li>
                    <li>• 尝试分步骤进行改写</li>
                    <li>• 参考预设规则模板</li>
                  </ul>
                </div>
              </details>

              <details className="border border-gray-200 rounded-lg">
                <summary className="p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50">
                  如何保持故事的连贯性？
                </summary>
                <div className="p-3 border-t border-gray-200 text-sm text-gray-700">
                  <ul className="space-y-1">
                    <li>• 在规则中强调保持主线剧情</li>
                    <li>• 避免大幅修改关键情节</li>
                    <li>• 注意角色性格的一致性</li>
                    <li>• 考虑前后章节的衔接</li>
                  </ul>
                </div>
              </details>

              <details className="border border-gray-200 rounded-lg">
                <summary className="p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50">
                  改写速度很慢怎么办？
                </summary>
                <div className="p-3 border-t border-gray-200 text-sm text-gray-700">
                  <ul className="space-y-1">
                    <li>• 减少单次改写的章节数量</li>
                    <li>• 简化规则描述</li>
                    <li>• 避免在高峰时段使用</li>
                    <li>• 检查网络连接状态</li>
                  </ul>
                </div>
              </details>
            </div>
          </section>
        </div>
      )
    },
    advanced: {
      title: '高级技巧',
      icon: Lightbulb,
      content: (
        <div className="space-y-6">
          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">组合规则策略</h3>
            <p className="text-gray-700 mb-4">
              对于复杂的改写需求，可以将多个简单规则组合使用，分阶段完成改写。
            </p>
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-3">分阶段改写示例</h4>
              <div className="space-y-3">
                <div className="bg-white rounded p-3 border-l-4 border-blue-500">
                  <div className="font-medium text-blue-800">第一阶段：清理内容</div>
                  <div className="text-sm text-gray-700">删除毒点情节，修正逻辑错误</div>
                </div>
                <div className="bg-white rounded p-3 border-l-4 border-green-500">
                  <div className="font-medium text-green-800">第二阶段：增强内容</div>
                  <div className="text-sm text-gray-700">扩写感情戏，增加互动描写</div>
                </div>
                <div className="bg-white rounded p-3 border-l-4 border-purple-500">
                  <div className="font-medium text-purple-800">第三阶段：优化细节</div>
                  <div className="text-sm text-gray-700">改善对话风格，调整节奏</div>
                </div>
              </div>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">针对性改写</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">都市小说</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 现代化语言表达</li>
                  <li>• 职场/校园场景描写</li>
                  <li>• 现实感情发展</li>
                  <li>• 社会背景融入</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">玄幻小说</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 修炼体系完善</li>
                  <li>• 世界观构建</li>
                  <li>• 战斗场面描写</li>
                  <li>• 境界提升逻辑</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">历史小说</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 历史背景考证</li>
                  <li>• 古代语言风格</li>
                  <li>• 政治军事描写</li>
                  <li>• 文化细节还原</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">言情小说</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 情感细腻描写</li>
                  <li>• 心理活动刻画</li>
                  <li>• 浪漫场景营造</li>
                  <li>• 角色魅力塑造</li>
                </ul>
              </div>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">质量控制</h3>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">改写质量检查清单</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="font-medium text-yellow-800 mb-1">内容质量</div>
                  <ul className="text-yellow-700 space-y-1">
                    <li>□ 故事逻辑完整</li>
                    <li>□ 角色行为合理</li>
                    <li>□ 情节发展自然</li>
                    <li>□ 语言表达流畅</li>
                  </ul>
                </div>
                <div>
                  <div className="font-medium text-yellow-800 mb-1">规则执行</div>
                  <ul className="text-yellow-700 space-y-1">
                    <li>□ 改写目标达成</li>
                    <li>□ 重点内容突出</li>
                    <li>□ 不需要内容简化</li>
                    <li>□ 整体风格统一</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
        </div>
      )
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="mb-8">
          <Link 
            href="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="mr-2" size={20} />
            返回主页
          </Link>
          <h1 className="text-3xl font-bold text-gray-800">改写规则帮助</h1>
          <p className="text-gray-600 mt-2">学习如何编写有效的改写规则，获得更好的改写效果</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-4 sticky top-4">
              <h2 className="font-semibold text-gray-800 mb-4">目录</h2>
              <nav className="space-y-2">
                {Object.entries(categories).map(([key, category]) => {
                  const Icon = category.icon;
                  return (
                    <button
                      key={key}
                      onClick={() => setSelectedCategory(key)}
                      className={`w-full text-left p-3 rounded-lg transition-colors flex items-center ${
                        selectedCategory === key
                          ? 'bg-blue-100 text-blue-800 border border-blue-200'
                          : 'hover:bg-gray-100 text-gray-700'
                      }`}
                    >
                      <Icon className="mr-3" size={18} />
                      {category.title}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* 主内容 */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-6">
                {React.createElement(categories[selectedCategory as keyof typeof categories].icon, {
                  className: "mr-3 text-blue-600",
                  size: 24
                })}
                <h2 className="text-2xl font-bold text-gray-800">
                  {categories[selectedCategory as keyof typeof categories].title}
                </h2>
              </div>
              {categories[selectedCategory as keyof typeof categories].content}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
