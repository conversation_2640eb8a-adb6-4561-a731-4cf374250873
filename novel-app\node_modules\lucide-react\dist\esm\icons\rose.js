/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M17 10h-1a4 4 0 1 1 4-4v.534", key: "7qf5zm" }],
  [
    "path",
    { d: "M17 6h1a4 4 0 0 1 1.42 7.74l-2.29.87a6 6 0 0 1-5.339-10.68l2.069-1.31", key: "1et29u" }
  ],
  [
    "path",
    {
      d: "M4.5 17c2.8-.5 4.4 0 5.5.8s1.8 2.2 2.3 3.7c-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2",
      key: "kiv2lz"
    }
  ],
  ["path", { d: "M9.77 12C4 15 2 22 2 22", key: "h28rw0" }],
  ["circle", { cx: "17", cy: "8", r: "2", key: "1330xn" }]
];
const Rose = createLucideIcon("rose", __iconNode);

export { __iconNode, Rose as default };
//# sourceMappingURL=rose.js.map
